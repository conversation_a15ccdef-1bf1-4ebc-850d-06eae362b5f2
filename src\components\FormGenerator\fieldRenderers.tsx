import React from 'react';
import {
  Input,
  InputNumber,
  Select,
  Radio,
  Checkbox,
  Switch,
  DatePicker,
  TimePicker,
  Upload,
  Slider,
  Rate,
  ColorPicker,
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { FormFieldConfig } from './formConfig';

const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { Dragger } = Upload;

// 字段渲染器类型定义
export type FieldRenderer = (field: FormFieldConfig) => React.ReactElement | null;

// 基础输入字段渲染器
export const renderInputFields: FieldRenderer = (field) => {
  const { type, placeholder, disabled, props = {} } = field;
  const commonProps = { placeholder, disabled, ...props };

  const inputFieldMap = {
    input: () => <Input {...commonProps} />,
    textarea: () => <TextArea {...commonProps} />,
    password: () => <Input.Password {...commonProps} />,
    email: () => <Input type="email" {...commonProps} />,
    number: () => <InputNumber {...commonProps} style={{ width: '100%' }} />,
  };

  const renderer = inputFieldMap[type as keyof typeof inputFieldMap];
  return renderer ? renderer() : null;
};

// 选择类字段渲染器
export const renderSelectionFields: FieldRenderer = (field) => {
  const { type, placeholder, disabled, options, props = {} } = field;
  const commonProps = { placeholder, disabled, ...props };

  const renderOptions = () => options?.map((option) => {
    const key = String(option.value);
    const optionProps = { 
      key, 
      value: option.value, 
      disabled: option.disabled 
    };

    switch (type) {
      case 'select':
        return <Option {...optionProps}>{option.label}</Option>;
      case 'radio':
        return <Radio {...optionProps}>{option.label}</Radio>;
      case 'checkbox':
        return <Checkbox {...optionProps}>{option.label}</Checkbox>;
      default:
        return null;
    }
  });

  const selectionFieldMap = {
    select: () => (
      <Select {...commonProps}>
        {renderOptions()}
      </Select>
    ),
    radio: () => (
      <Radio.Group disabled={disabled} {...props}>
        {renderOptions()}
      </Radio.Group>
    ),
    checkbox: () => (
      <Checkbox.Group disabled={disabled} {...props}>
        {renderOptions()}
      </Checkbox.Group>
    ),
  };

  const renderer = selectionFieldMap[type as keyof typeof selectionFieldMap];
  return renderer ? renderer() : null;
};

// 日期时间字段渲染器
export const renderDateTimeFields: FieldRenderer = (field) => {
  const { type, placeholder, disabled, props = {} } = field;
  const commonProps = { 
    placeholder, 
    disabled, 
    style: { width: '100%' }, 
    ...props 
  };

  const dateTimeFieldMap = {
    date: () => <DatePicker {...commonProps} />,
    datetime: () => <DatePicker showTime {...commonProps} />,
    time: () => <TimePicker {...commonProps} />,
    daterange: () => <RangePicker disabled={disabled} style={{ width: '100%' }} {...props} />,
  };

  const renderer = dateTimeFieldMap[type as keyof typeof dateTimeFieldMap];
  return renderer ? renderer() : null;
};

// 特殊字段渲染器
export const renderSpecialFields: FieldRenderer = (field) => {
  const { type, disabled, props = {} } = field;

  const specialFieldMap = {
    switch: () => <Switch disabled={disabled} {...props} />,
    slider: () => <Slider disabled={disabled} {...props} />,
    rate: () => <Rate disabled={disabled} {...props} />,
    color: () => <ColorPicker disabled={disabled} {...props} />,
    upload: () => (
      <Dragger disabled={disabled} {...props}>
        <p className="ant-upload-drag-icon">
          <UploadOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p className="ant-upload-hint">支持单个或批量上传</p>
      </Dragger>
    ),
  };

  const renderer = specialFieldMap[type as keyof typeof specialFieldMap];
  return renderer ? renderer() : null;
};

// 默认字段渲染器
export const renderDefaultField: FieldRenderer = (field) => {
  const { placeholder, disabled, props = {} } = field;
  return <Input placeholder={placeholder} disabled={disabled} {...props} />;
};

// 主字段渲染函数
export const renderField = (field: FormFieldConfig): React.ReactElement => {
  const fieldRenderers: FieldRenderer[] = [
    renderInputFields,
    renderSelectionFields, 
    renderDateTimeFields,
    renderSpecialFields,
  ];

  // 尝试每个渲染器，返回第一个成功的结果
  for (const renderer of fieldRenderers) {
    const result = renderer(field);
    if (result) return result;
  }

  // 默认回退到普通输入框
  return renderDefaultField(field)!;
};
