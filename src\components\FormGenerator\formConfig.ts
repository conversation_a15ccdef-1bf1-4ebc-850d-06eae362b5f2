// 表单字段类型枚举
export type FormFieldType = 
  | 'input'
  | 'textarea' 
  | 'number'
  | 'password'
  | 'email'
  | 'select'
  | 'radio'
  | 'checkbox'
  | 'switch'
  | 'date'
  | 'datetime'
  | 'time'
  | 'daterange'
  | 'upload'
  | 'slider'
  | 'rate'
  | 'color';

// 验证规则接口
export interface ValidationRule {
  required?: boolean;
  message?: string;
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (rule: unknown, value: unknown) => Promise<void>;
}

// 选项接口（用于select、radio、checkbox等）
export interface Option {
  label: string;
  value: string | number | boolean;
  disabled?: boolean;
}

// 表单字段配置接口
export interface FormFieldConfig {
  name: string;
  label: string;
  type: FormFieldType;
  placeholder?: string;
  defaultValue?: unknown;
  rules?: ValidationRule[];
  options?: Option[];
  disabled?: boolean;
  hidden?: boolean;
  props?: Record<string, unknown>; // 额外的antd组件属性
  span?: number; // 栅格布局占用列数
  dependencies?: string[]; // 依赖的其他字段
  condition?: (values: Record<string, unknown>) => boolean; // 显示条件
}

// 表单配置接口
export interface FormConfig {
  title?: string;
  layout?: 'horizontal' | 'vertical' | 'inline';
  labelCol?: { span: number };
  wrapperCol?: { span: number };
  size?: 'small' | 'middle' | 'large';
  fields: FormFieldConfig[];
  submitText?: string;
  resetText?: string;
  showSubmit?: boolean;
  showReset?: boolean;
  onSubmit?: (values: Record<string, unknown>) => void;
  onReset?: () => void;
  onChange?: (changedValues: Record<string, unknown>, allValues: Record<string, unknown>) => void;
} 