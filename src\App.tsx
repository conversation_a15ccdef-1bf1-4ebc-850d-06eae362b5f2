import { useState } from 'react';
import { Layout, Select} from 'antd';
import FormGenerator from './components/FormGenerator/FormGenerator';
import { simpleForm, userRegistrationForm, productForm, meetingForm } from './formExamples';
import './App.css';

const { Content } = Layout;
function App() {


  const formExamples = {
    simple: { name: '简单表单', config: simpleForm },
    registration: { name: '用户注册', config: userRegistrationForm },
    product: { name: '商品录入', config: productForm },
    meeting: { name: '会议安排', config: meetingForm }
  };

  const [selectedForm, setSelectedForm] = useState<keyof typeof formExamples>('simple');
  const currentConfig = formExamples[selectedForm].config;

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Content style={{ display: 'flex', height: '100vh' }}>
        {/* 左侧配置面板 */}
        <div className="config-panel">
          <Select
            style={{ width: '200px' }}
            value={selectedForm}
            onChange={setSelectedForm}
            options={Object.entries(formExamples).map(([key, value]) => ({
              label: value.name,
              value: key
            }))}
          />
          <div className="config-json">
            <pre>
              {JSON.stringify(currentConfig, null, 2)}
            </pre>
          </div>
        </div>

        {/* 右侧表单展示 */}
        <div className="form-preview">
          <FormGenerator config={currentConfig} />
        </div>
      </Content>
    </Layout>
  );
}

export default App;
