import React, { useEffect } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Select,
  Radio,
  Checkbox,
  Switch,
  DatePicker,
  TimePicker,
  Upload,
  Slider,
  Rate,
  Button,
  Row,
  Col,
  Card,
  ColorPicker,
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { FormConfig, FormFieldConfig } from './formConfig';

const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { Dragger } = Upload;

interface FormGeneratorProps {
  config: FormConfig;
}

const FormGenerator: React.FC<FormGeneratorProps> = ({ config }) => {
  const [form] = Form.useForm();

  // 设置默认值
  useEffect(() => {
    const initialValues: Record<string, unknown> = {};
    config.fields.forEach((field) => {
      if (field.defaultValue !== undefined) {
        initialValues[field.name] = field.defaultValue;
      }
    });
    form.setFieldsValue(initialValues);
  }, [config.fields, form]);

  // 渲染表单字段
  const renderField = (field: FormFieldConfig) => {
    const { type, placeholder, options, props = {}, disabled } = field;

    switch (type) {
      case 'input':
        return <Input placeholder={placeholder} disabled={disabled} {...props} />;
      
      case 'textarea':
        return <TextArea placeholder={placeholder} disabled={disabled} {...props} />;
      
      case 'number':
        return <InputNumber placeholder={placeholder} disabled={disabled} style={{ width: '100%' }} {...props} />;
      
      case 'password':
        return <Input.Password placeholder={placeholder} disabled={disabled} {...props} />;
      
      case 'email':
        return <Input type="email" placeholder={placeholder} disabled={disabled} {...props} />;
      
      case 'select':
        return (
          <Select placeholder={placeholder} disabled={disabled} {...props}>
            {options?.map((option) => (
              <Option key={String(option.value)} value={option.value} disabled={option.disabled}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
      
      case 'radio':
        return (
          <Radio.Group disabled={disabled} {...props}>
            {options?.map((option) => (
              <Radio key={String(option.value)} value={option.value} disabled={option.disabled}>
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        );
      
      case 'checkbox':
        return (
          <Checkbox.Group disabled={disabled} {...props}>
            {options?.map((option) => (
              <Checkbox key={String(option.value)} value={option.value} disabled={option.disabled}>
                {option.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        );
      
      case 'switch':
        return <Switch disabled={disabled} {...props} />;
      
      case 'date':
        return <DatePicker placeholder={placeholder} disabled={disabled} style={{ width: '100%' }} {...props} />;
      
      case 'datetime':
        return <DatePicker showTime placeholder={placeholder} disabled={disabled} style={{ width: '100%' }} {...props} />;
      
      case 'time':
        return <TimePicker placeholder={placeholder} disabled={disabled} style={{ width: '100%' }} {...props} />;
      
      case 'daterange':
        return <RangePicker disabled={disabled} style={{ width: '100%' }} {...props} />;
      
      case 'upload':
        return (
          <Dragger disabled={disabled} {...props}>
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">支持单个或批量上传</p>
          </Dragger>
        );
      
      case 'slider':
        return <Slider disabled={disabled} {...props} />;
      
      case 'rate':
        return <Rate disabled={disabled} {...props} />;
      
      case 'color':
        return <ColorPicker disabled={disabled} {...props} />;
      
      default:
        return <Input placeholder={placeholder} disabled={disabled} {...props} />;
    }
  };

  // 处理表单提交
  const handleSubmit = (values: Record<string, unknown>) => {
    console.log('表单数据:', values);
    config.onSubmit?.(values);
  };

  // 处理表单重置
  const handleReset = () => {
    form.resetFields();
    config.onReset?.();
  };

  // 处理表单值变化
  const handleValuesChange = (changedValues: Record<string, unknown>, allValues: Record<string, unknown>) => {
    config.onChange?.(changedValues, allValues);
  };

  // 过滤需要显示的字段
  const getVisibleFields = () => {
    const allValues = form.getFieldsValue();
    return config.fields.filter((field) => {
      if (field.hidden) return false;
      if (field.condition) {
        return field.condition(allValues);
      }
      return true;
    });
  };

  const visibleFields = getVisibleFields();

  return (
    <Card title={config.title} style={{ maxWidth: 800, margin: '0 auto' }}>
      <Form
        form={form}
        layout={config.layout || 'vertical'}
        labelCol={config.labelCol}
        wrapperCol={config.wrapperCol}
        size={config.size || 'middle'}
        onFinish={handleSubmit}
        onValuesChange={handleValuesChange}
        autoComplete="off"
      >
        <Row gutter={16}>
          {visibleFields.map((field) => (
            <Col key={field.name} span={field.span || 24}>
              <Form.Item
                name={field.name}
                label={field.label}
                rules={field.rules?.map(rule => ({
                  required: rule.required,
                  message: rule.message,
                  min: rule.min,
                  max: rule.max,
                  pattern: rule.pattern,
                  validator: rule.validator,
                }))}
                dependencies={field.dependencies}
              >
                {renderField(field)}
              </Form.Item>
            </Col>
          ))}
        </Row>
        
        {(config.showSubmit !== false || config.showReset !== false) && (
          <Form.Item style={{ textAlign: 'center', marginTop: 24 }}>
            {config.showSubmit !== false && (
              <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
                {config.submitText || '提交'}
              </Button>
            )}
            {config.showReset !== false && (
              <Button onClick={handleReset}>
                {config.resetText || '重置'}
              </Button>
            )}
          </Form.Item>
        )}
      </Form>
    </Card>
  );
};

export default FormGenerator; 