/* 左侧配置面板 */
.config-panel {
  width: 50%;
  padding: 24px;
  background-color: #fafafa;
  border-right: 1px solid #d9d9d9;
  overflow: auto;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 右侧表单展示 */
.form-preview {
  width: 50%;
  padding: 24px;
  background-color: #fff;
  overflow: auto;
  height: 100vh;
}

/* JSON配置显示区域 */
.config-json {
  background: #fff;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}

.config-json pre {
  margin: 0;
  color: #262626;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .config-panel,
  .form-preview {
    width: 100%;
    height: 50vh;
  }
  
  .config-json {
    max-height: 35vh;
  }
}

@media (max-width: 768px) {
  .config-panel,
  .form-preview {
    padding: 16px;
  }
  
  .config-json {
    font-size: 11px;
    max-height: 30vh;
  }
}

