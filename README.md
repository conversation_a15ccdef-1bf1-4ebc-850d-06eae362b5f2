# 🚀 动态表单生成器

一个基于 Vite + React + TypeScript + Ant Design 的动态表单生成器，支持通过配置快速生成各种类型的表单。

## ✨ 功能特性

- 🎯 **多种输入控件**：支持文本框、数字输入、下拉选择、单选框、复选框、开关、日期选择、文件上传、滑块、评分、颜色选择器等
- 🔒 **表单验证**：支持必填验证、长度限制、正则表达式、自定义验证函数
- 📱 **响应式布局**：基于栅格系统，支持自适应不同屏幕尺寸
- 👁️ **条件显示**：基于其他字段值动态显示/隐藏字段
- 🔗 **字段依赖**：支持字段间的依赖关系
- ⚙️ **灵活配置**：通过 JSON 配置快速生成表单
- 🔧 **TypeScript 支持**：完整的类型定义，提供优秀的开发体验
- 🎨 **现代化 UI**：基于 Ant Design，界面美观且易用

## 🛠️ 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI 库**: Ant Design
- **样式**: CSS3 + 响应式设计

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 📋 表单配置说明

### 基本配置结构

```typescript
interface FormConfig {
  title?: string;                    // 表单标题
  layout?: 'horizontal' | 'vertical' | 'inline'; // 布局方式
  labelCol?: { span: number };       // 标签列宽度
  wrapperCol?: { span: number };     // 包装器列宽度
  size?: 'small' | 'middle' | 'large'; // 表单尺寸
  fields: FormFieldConfig[];         // 字段配置数组
  submitText?: string;               // 提交按钮文本
  resetText?: string;                // 重置按钮文本
  showSubmit?: boolean;              // 是否显示提交按钮
  showReset?: boolean;               // 是否显示重置按钮
  onSubmit?: (values: Record<string, unknown>) => void; // 提交回调
  onReset?: () => void;              // 重置回调
  onChange?: (changedValues: any, allValues: Record<string, unknown>) => void; // 值变化回调
}
```

### 字段配置

```typescript
interface FormFieldConfig {
  name: string;                      // 字段名称
  label: string;                     // 字段标签
  type: FormFieldType;               // 字段类型
  placeholder?: string;              // 占位符
  defaultValue?: unknown;            // 默认值
  rules?: ValidationRule[];          // 验证规则
  options?: Option[];                // 选项（用于 select、radio、checkbox）
  disabled?: boolean;                // 是否禁用
  hidden?: boolean;                  // 是否隐藏
  props?: Record<string, unknown>;   // 额外的组件属性
  span?: number;                     // 栅格布局占用列数（1-24）
  dependencies?: string[];           // 依赖的其他字段
  condition?: (values: Record<string, unknown>) => boolean; // 显示条件
}
```

### 支持的字段类型

| 类型 | 说明 | 对应组件 |
|------|------|----------|
| `input` | 文本输入框 | Input |
| `textarea` | 多行文本输入 | TextArea |
| `number` | 数字输入框 | InputNumber |
| `password` | 密码输入框 | Input.Password |
| `email` | 邮箱输入框 | Input |
| `select` | 下拉选择 | Select |
| `radio` | 单选框组 | Radio.Group |
| `checkbox` | 复选框组 | Checkbox.Group |
| `switch` | 开关 | Switch |
| `date` | 日期选择 | DatePicker |
| `datetime` | 日期时间选择 | DatePicker |
| `time` | 时间选择 | TimePicker |
| `daterange` | 日期范围选择 | RangePicker |
| `upload` | 文件上传 | Upload |
| `slider` | 滑块 | Slider |
| `rate` | 评分 | Rate |
| `color` | 颜色选择器 | ColorPicker |

### 验证规则

```typescript
interface ValidationRule {
  required?: boolean;                // 是否必填
  message?: string;                  // 错误信息
  min?: number;                      // 最小值/长度
  max?: number;                      // 最大值/长度
  pattern?: RegExp;                  // 正则表达式
  validator?: (rule: unknown, value: unknown) => Promise<void>; // 自定义验证函数
}
```

## 📖 使用示例

### 简单表单配置

```typescript
const simpleForm: FormConfig = {
  title: '用户信息',
  layout: 'vertical',
  fields: [
    {
      name: 'username',
      label: '用户名',
      type: 'input',
      placeholder: '请输入用户名',
      rules: [
        { required: true, message: '用户名不能为空' },
        { min: 3, message: '用户名至少3个字符' }
      ]
    },
    {
      name: 'email',
      label: '邮箱',
      type: 'email',
      placeholder: '请输入邮箱',
      rules: [
        { required: true, message: '邮箱不能为空' },
        { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }
      ]
    },
    {
      name: 'age',
      label: '年龄',
      type: 'number',
      props: { min: 1, max: 150 }
    }
  ],
  onSubmit: (values) => {
    console.log('表单数据:', values);
  }
};
```

### 带条件显示的表单

```typescript
const conditionalForm: FormConfig = {
  title: '会议安排',
  fields: [
    {
      name: 'meetingType',
      label: '会议类型',
      type: 'select',
      options: [
        { label: '线上会议', value: 'online' },
        { label: '线下会议', value: 'offline' }
      ]
    },
    {
      name: 'location',
      label: '会议地点',
      type: 'input',
      condition: (values) => values.meetingType === 'offline', // 只有选择线下会议时才显示
      rules: [{ required: true, message: '请输入会议地点' }]
    },
    {
      name: 'link',
      label: '会议链接',
      type: 'input',
      condition: (values) => values.meetingType === 'online', // 只有选择线上会议时才显示
      rules: [{ required: true, message: '请输入会议链接' }]
    }
  ]
};
```

### 在组件中使用

```tsx
import FormGenerator from './components/FormGenerator';
import { simpleForm } from './config/formExamples';

function App() {
  return (
    <div>
      <FormGenerator config={simpleForm} />
    </div>
  );
}
```

## 🎨 自定义样式

项目使用 CSS 模块化和 Ant Design 主题系统，你可以通过以下方式自定义样式：

1. **修改 App.css**：调整全局样式和组件样式
2. **覆盖 Ant Design 样式**：使用 CSS 选择器覆盖默认样式
3. **响应式设计**：已内置移动端适配样式

## 📁 项目结构

```
src/
├── components/
│   └── FormGenerator.tsx    # 表单生成器核心组件
├── types/
│   └── formConfig.ts        # TypeScript 类型定义
├── config/
│   └── formExamples.ts      # 示例表单配置
├── App.tsx                  # 主应用组件
├── App.css                  # 全局样式
└── main.tsx                 # 应用入口
```

## 🔧 扩展功能

### 添加新的字段类型

1. 在 `types/formConfig.ts` 中添加新的字段类型
2. 在 `FormGenerator.tsx` 的 `renderField` 方法中添加对应的渲染逻辑

### 自定义验证规则

```typescript
{
  name: 'custom',
  label: '自定义字段',
  type: 'input',
  rules: [
    {
      validator: async (_, value) => {
        if (value && value.length < 5) {
          return Promise.reject(new Error('长度不能少于5个字符'));
        }
        return Promise.resolve();
      }
    }
  ]
}
```

## 🌟 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交改动 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 提交 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [React](https://reactjs.org/) - 用户界面构建库
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Ant Design](https://ant.design/) - 企业级 UI 设计语言
- [TypeScript](https://www.typescriptlang.org/) - JavaScript 的超集

---

如果这个项目对你有帮助，请给个 ⭐ Star 支持一下！
