// 导出核心组件
export { default as FormGenerator } from './FormGenerator';

// 导出类型定义
export type {
  FormConfig,
  FormFieldConfig,
  FormFieldType,
  ValidationRule,
  Option
} from './formConfig';

// 导出字段渲染器
export {
  renderField,
  renderInputFields,
  renderSelectionFields,
  renderDateTimeFields,
  renderSpecialFields,
  renderDefaultField,
} from './fieldRenderers';

export type { FieldRenderer } from './fieldRenderers';