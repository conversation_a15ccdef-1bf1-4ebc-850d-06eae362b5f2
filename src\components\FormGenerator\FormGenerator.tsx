import React, { useEffect } from 'react';
import {
  Form,
  Button,
  Row,
  Col,
  Card,
} from 'antd';
import type { FormConfig } from './formConfig';
import { renderField } from './fieldRenderers';

interface FormGeneratorProps {
  config: FormConfig;
}

const FormGenerator: React.FC<FormGeneratorProps> = ({ config }) => {
  const [form] = Form.useForm();

  // 设置默认值
  useEffect(() => {
    const initialValues: Record<string, unknown> = {};
    config.fields.forEach((field) => {
      if (field.defaultValue !== undefined) {
        initialValues[field.name] = field.defaultValue;
      }
    });
    form.setFieldsValue(initialValues);
  }, [config.fields, form]);

  // 处理表单提交
  const handleSubmit = (values: Record<string, unknown>) => {
    console.log('表单数据:', values);
    config.onSubmit?.(values);
  };

  // 处理表单重置
  const handleReset = () => {
    form.resetFields();
    config.onReset?.();
  };

  // 处理表单值变化
  const handleValuesChange = (changedValues: Record<string, unknown>, allValues: Record<string, unknown>) => {
    config.onChange?.(changedValues, allValues);
  };

  // 过滤需要显示的字段
  const getVisibleFields = () => {
    const allValues = form.getFieldsValue();
    return config.fields.filter((field) => {
      if (field.hidden) return false;
      if (field.condition) {
        return field.condition(allValues);
      }
      return true;
    });
  };

  const visibleFields = getVisibleFields();

  return (
    <Card title={config.title} style={{ maxWidth: 800, margin: '0 auto' }}>
      <Form
        form={form}
        layout={config.layout || 'vertical'}
        labelCol={config.labelCol}
        wrapperCol={config.wrapperCol}
        size={config.size || 'middle'}
        onFinish={handleSubmit}
        onValuesChange={handleValuesChange}
        autoComplete="off"
      >
        <Row gutter={16}>
          {visibleFields.map((field) => (
            <Col key={field.name} span={field.span || 24}>
              <Form.Item
                name={field.name}
                label={field.label}
                rules={field.rules?.map(rule => ({
                  required: rule.required,
                  message: rule.message,
                  min: rule.min,
                  max: rule.max,
                  pattern: rule.pattern,
                  validator: rule.validator,
                }))}
                dependencies={field.dependencies}
              >
                {renderField(field)}
              </Form.Item>
            </Col>
          ))}
        </Row>
        
        {(config.showSubmit !== false || config.showReset !== false) && (
          <Form.Item style={{ textAlign: 'center', marginTop: 24 }}>
            {config.showSubmit !== false && (
              <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
                {config.submitText || '提交'}
              </Button>
            )}
            {config.showReset !== false && (
              <Button onClick={handleReset}>
                {config.resetText || '重置'}
              </Button>
            )}
          </Form.Item>
        )}
      </Form>
    </Card>
  );
};

export default FormGenerator; 