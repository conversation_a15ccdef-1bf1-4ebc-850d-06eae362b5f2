import type { FormConfig } from './components/FormGenerator/formConfig';

// 简单表单配置
export const simpleForm: FormConfig = {
  title: '简单表单示例',
  layout: 'vertical',
  fields: [
    {
      name: 'name',
      label: '姓名',
      type: 'input',
      placeholder: '请输入您的姓名',
      span: 24,
      rules: [{ required: true, message: '姓名不能为空' }]
    },
    {
      name: 'email',
      label: '邮箱',
      type: 'email',
      placeholder: '请输入邮箱地址',
      span: 24,
      rules: [{ required: true, message: '邮箱不能为空' }]
    },
    {
      name: 'age',
      label: '年龄',
      type: 'number',
      placeholder: '请输入年龄',
      span: 12,
      props: { min: 1, max: 150 }
    },
    {
      name: 'city',
      label: '所在城市',
      type: 'select',
      placeholder: '请选择城市',
      span: 12,
      options: [
        { label: '北京', value: 'beijing' },
        { label: '上海', value: 'shanghai' },
        { label: '深圳', value: 'shenzhen' },
        { label: '广州', value: 'guangzhou' }
      ]
    },
    {
      name: 'newsletter',
      label: '订阅邮件',
      type: 'switch',
      span: 12,
      defaultValue: false
    },
    {
      name: 'rating',
      label: '满意度评分',
      type: 'rate',
      span: 12,
      defaultValue: 5
    }
  ],
  onSubmit: (values) => {
    console.log('表单数据:', values);
    alert('表单提交成功！');
  }
};

// 用户注册表单配置
export const userRegistrationForm: FormConfig = {
  title: '用户注册表单',
  layout: 'vertical',
  size: 'large',
  fields: [
    {
      name: 'username',
      label: '用户名',
      type: 'input',
      placeholder: '请输入用户名',
      span: 12,
      rules: [
        { required: true, message: '用户名不能为空' },
        { min: 3, message: '用户名至少3个字符' },
        { max: 20, message: '用户名最多20个字符' },
        { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
      ]
    },
    {
      name: 'email',
      label: '邮箱',
      type: 'email',
      placeholder: '请输入邮箱地址',
      span: 12,
      rules: [
        { required: true, message: '邮箱不能为空' },
        { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入有效的邮箱地址' }
      ]
    },
    {
      name: 'password',
      label: '密码',
      type: 'password',
      placeholder: '请输入密码',
      span: 12,
      rules: [
        { required: true, message: '密码不能为空' },
        { min: 6, message: '密码至少6个字符' }
      ]
    },
    {
      name: 'confirmPassword',
      label: '确认密码',
      type: 'password',
      placeholder: '请再次输入密码',
      span: 12,
      dependencies: ['password'],
      rules: [
        { required: true, message: '确认密码不能为空' },
        {
          validator: async (_, value) => {
            // 这里需要获取表单实例来比较密码
            if (!value || value === '') {
              return Promise.reject(new Error('请确认密码'));
            }
            return Promise.resolve();
          }
        }
      ]
    },
    {
      name: 'age',
      label: '年龄',
      type: 'number',
      placeholder: '请输入年龄',
      span: 8,
      props: { min: 1, max: 150 },
      rules: [
        { required: true, message: '年龄不能为空' }
      ]
    },
    {
      name: 'gender',
      label: '性别',
      type: 'radio',
      span: 8,
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' },
        { label: '其他', value: 'other' }
      ],
      rules: [
        { required: true, message: '请选择性别' }
      ]
    },
    {
      name: 'phone',
      label: '手机号',
      type: 'input',
      placeholder: '请输入手机号',
      span: 8,
      rules: [
        { required: true, message: '手机号不能为空' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
      ]
    },
    {
      name: 'interests',
      label: '兴趣爱好',
      type: 'checkbox',
      span: 24,
      options: [
        { label: '编程', value: 'programming' },
        { label: '阅读', value: 'reading' },
        { label: '音乐', value: 'music' },
        { label: '运动', value: 'sports' },
        { label: '旅行', value: 'travel' },
        { label: '摄影', value: 'photography' }
      ]
    },
    {
      name: 'bio',
      label: '个人简介',
      type: 'textarea',
      placeholder: '请输入个人简介',
      span: 24,
      props: { rows: 4, maxLength: 200, showCount: true }
    }
  ],
  onSubmit: (values) => {
    console.log('用户注册数据:', values);
    alert('注册成功！');
  }
};

// 商品信息表单配置
export const productForm: FormConfig = {
  title: '商品信息录入',
  layout: 'vertical',
  fields: [
    {
      name: 'productName',
      label: '商品名称',
      type: 'input',
      placeholder: '请输入商品名称',
      span: 12,
      rules: [
        { required: true, message: '商品名称不能为空' }
      ]
    },
    {
      name: 'category',
      label: '商品分类',
      type: 'select',
      placeholder: '请选择商品分类',
      span: 12,
      options: [
        { label: '电子产品', value: 'electronics' },
        { label: '服装鞋帽', value: 'clothing' },
        { label: '食品饮料', value: 'food' },
        { label: '家居用品', value: 'home' },
        { label: '图书文具', value: 'books' }
      ],
      rules: [
        { required: true, message: '请选择商品分类' }
      ]
    },
    {
      name: 'price',
      label: '商品价格（元）',
      type: 'number',
      placeholder: '请输入价格',
      span: 8,
      props: { min: 0, precision: 2, step: 0.01 },
      rules: [
        { required: true, message: '价格不能为空' }
      ]
    },
    {
      name: 'stock',
      label: '库存数量',
      type: 'number',
      placeholder: '请输入库存',
      span: 8,
      props: { min: 0 },
      rules: [
        { required: true, message: '库存不能为空' }
      ]
    },
    {
      name: 'rating',
      label: '商品评分',
      type: 'rate',
      span: 8,
      defaultValue: 5
    },
    {
      name: 'isOnSale',
      label: '是否上架',
      type: 'switch',
      span: 6,
      defaultValue: true
    },
    {
      name: 'isFeatured',
      label: '是否推荐',
      type: 'switch',
      span: 6,
      defaultValue: false
    },
    {
      name: 'releaseDate',
      label: '发布日期',
      type: 'date',
      span: 12,
      rules: [
        { required: true, message: '请选择发布日期' }
      ]
    },
    {
      name: 'description',
      label: '商品描述',
      type: 'textarea',
      placeholder: '请输入商品描述',
      span: 24,
      props: { rows: 6 },
      rules: [
        { required: true, message: '商品描述不能为空' }
      ]
    },
    {
      name: 'images',
      label: '商品图片',
      type: 'upload',
      span: 24,
      props: {
        multiple: true,
        listType: 'picture-card',
        maxCount: 5
      }
    }
  ],
  onSubmit: (values) => {
    console.log('商品信息:', values);
    alert('商品信息保存成功！');
  }
};

// 会议安排表单配置
export const meetingForm: FormConfig = {
  title: '会议安排',
  layout: 'horizontal',
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
  fields: [
    {
      name: 'meetingTitle',
      label: '会议主题',
      type: 'input',
      placeholder: '请输入会议主题',
      rules: [
        { required: true, message: '会议主题不能为空' }
      ]
    },
    {
      name: 'meetingType',
      label: '会议类型',
      type: 'select',
      placeholder: '请选择会议类型',
      options: [
        { label: '线上会议', value: 'online' },
        { label: '线下会议', value: 'offline' },
        { label: '混合会议', value: 'hybrid' }
      ],
      rules: [
        { required: true, message: '请选择会议类型' }
      ]
    },
    {
      name: 'dateRange',
      label: '会议时间',
      type: 'daterange',
      rules: [
        { required: true, message: '请选择会议时间' }
      ]
    },
    {
      name: 'location',
      label: '会议地点',
      type: 'input',
      placeholder: '请输入会议地点',
      condition: (values) => values.meetingType === 'offline' || values.meetingType === 'hybrid',
      rules: [
        { required: true, message: '请输入会议地点' }
      ]
    },
    {
      name: 'onlineLink',
      label: '在线链接',
      type: 'input',
      placeholder: '请输入在线会议链接',
      condition: (values) => values.meetingType === 'online' || values.meetingType === 'hybrid',
      rules: [
        { required: true, message: '请输入在线会议链接' }
      ]
    },
    {
      name: 'participants',
      label: '参会人员',
      type: 'textarea',
      placeholder: '请输入参会人员，每行一个',
      props: { rows: 4 },
      rules: [
        { required: true, message: '请输入参会人员' }
      ]
    },
    {
      name: 'agenda',
      label: '会议议程',
      type: 'textarea',
      placeholder: '请输入会议议程',
      props: { rows: 6 }
    },
    {
      name: 'priority',
      label: '优先级',
      type: 'slider',
      props: {
        min: 1,
        max: 5,
        marks: {
          1: '低',
          2: '较低',
          3: '中等',
          4: '较高',
          5: '高'
        }
      },
      defaultValue: 3
    }
  ],
  onSubmit: (values) => {
    console.log('会议安排:', values);
    alert('会议安排保存成功！');
  }
}; 